<?php

namespace app\model;

use app\lib\llm\Exception;
use think\annotation\model\option\Append;
use think\annotation\model\option\Type;
use think\db\Query;
use think\exception\ValidateException;
use think\helper\Str;

/**
 * Class app\model\Model
 *
 * @property int $channel_id 渠道ID
 * @property int $id
 * @property int $sort
 * @property int $status
 * @property string $code
 * @property string $description
 * @property string $factor
 * @property string $label
 * @property string $params
 * @property string $type
 * @property string $version
 * @property-read \app\model\Channel $channel
 * @property-read mixed $checkpoint
 */
#[Type('params', 'json')]
#[Append(['features'])]
class Model extends \think\Model
{
    const STATUS_HIDE = 2;

    public function channel()
    {
        return $this->belongsTo(Channel::class);
    }

    /**
     * @param $method
     * @return \app\lib\llm\channel\Driver
     */
    public function getDriver($method = null)
    {
        $driver = $this->channel->driver;
        $class  = "\\app\\lib\\llm\\channel\\{$driver}\\" . Str::studly($this->type);
        if (!class_exists($class)) {
            throw new Exception("not support [{$driver}]");
        }

        if ($method) {
            $factor = $this->getFactor($method);

            if (is_null($factor)) {
                throw new ValidateException("The model `{$this->code}` does not support `{$method}` method");
            }
        }

        $options = [
            'type'       => $this->type,
            'code'       => $this->code,
            'checkpoint' => $this->checkpoint,
            'factor'     => $factor ?? null,
            'params'     => $this->params,
            'base_uri'   => $this->channel->base_uri,
            'auth'       => $this->channel->getAuth(),
        ];

        return new $class($options);
    }

    public function getFactor($method)
    {
        $factor = $this->factor;
        switch ($this->type) {
            case 'image':
            case 'text':
                return $factor[$method] ?? null;
            case 'music':
                if ($method == 'query') {
                    return 0;
                }
                return $factor[$method] ?? null;
        }
        return $factor;
    }

    public function scopeList(Query $query, $type = null, $status = null)
    {
        $query = $query->order('type asc,sort asc');

        if ($type) {
            $query->where('type', $type);
        }

        $query->whereRaw('status & 2 = 0');

        if (!empty($status)) {
            $query->whereRaw("status & {$status} > 0");
        }

        $query->hidden(['version']);
    }

    protected function getFactorAttr($factor)
    {
        switch ($this->type) {
            case 'image':
            case 'text':
            case 'music':
                return json_decode($factor, true);
        }

        return $factor;
    }

    protected function setFactorAttr($factor)
    {
        switch ($this->type) {
            case 'image':
            case 'text':
            case 'music':
                return json_encode($factor);
        }

        return $factor;
    }

    protected function getCheckpointAttr()
    {
        if (!empty($this->version)) {
            return $this->version;
        }
        return $this->code;
    }

    protected function getFeaturesAttr()
    {
        try {
            return $this->getDriver()->getFeatures($this->checkpoint);
        } catch (\Throwable) {
            return [];
        }
    }
}
