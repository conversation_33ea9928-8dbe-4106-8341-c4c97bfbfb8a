<?php

namespace app\model;

use Firebase\JWT\JWT;
use think\annotation\model\option\Hidden;
use think\Model;

/**
 * Class app\model\Admin
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property string $password
 * @property string $token
 * @property string $username
 */
#[Hidden(['password'])]
class Admin extends Model
{
    protected function getTokenAttr()
    {
        return JWT::encode([
            'exp' => time() + 60 * 60 * 24 * 3,
            'iat' => time(),
            'id'  => $this->id,
        ], config('app.token'), 'HS256');
    }
}
