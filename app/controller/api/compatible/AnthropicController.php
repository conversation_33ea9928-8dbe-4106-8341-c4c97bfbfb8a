<?php

namespace app\controller\api\compatible;

use app\controller\api\Controller;
use think\annotation\route\Post;
use function think\swoole\helper\iterator;

class AnthropicController extends Controller
{
    #[Post('anthropic/v1/messages')]
    public function index()
    {
        $params = $this->validate([
            'model'       => 'require',
            'system'      => '',
            'messages'    => 'require|array',
            'tools'       => 'array',
            'max_tokens'  => 'integer',
            'temperature' => 'float',
            'stream'      => 'bool',
        ]);



        $result = $this->llm->chat()->completions($params);

        $result->rewind();

        if (!$result->valid()) {
            return json($result->getReturn());
        }

        $generator = function () use ($result) {
            foreach ($result as $event) {
                yield 'data: ' . json_encode($event) . "\n\n";
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
