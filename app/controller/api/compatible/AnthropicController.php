<?php

namespace app\controller\api\compatible;

use app\controller\api\Controller;
use think\annotation\route\Post;
use function think\swoole\helper\iterator;

class AnthropicController extends Controller
{
    #[Post('anthropic/v1/messages')]
    public function index()
    {
        $params = $this->validate([
            'model'          => 'require',
            'system'         => '',
            'messages'       => 'require|array',
            'tools'          => 'array',
            'tool_choice'    => 'array',
            'max_tokens'     => 'integer',
            'temperature'    => 'float',
            'top_k'          => 'integer',
            'top_p'          => 'float',
            'stop_sequences' => 'array',
            'stream'         => 'bool',
            'thinking'       => 'array',
            'metadata'       => 'array',
        ]);

        // 转换 Anthropic 格式到 OpenAI 格式
        $params = $this->convertAnthropicToOpenAI($params);

        $result = $this->llm->chat()->completions($params);

        $result->rewind();

        if (!$result->valid()) {
            // 转换错误响应为 Anthropic 格式
            return json($this->convertErrorToAnthropic($result->getReturn()));
        }

        $stream = $params['stream'] ?? true;

        if ($stream) {
            $generator = function () use ($result, $params) {
                $messageId = 'msg_' . uniqid();
                $model = $params['model'] ?? 'claude-3-sonnet';

                // 发送 message_start 事件
                yield 'data: ' . json_encode([
                    'type' => 'message_start',
                    'message' => [
                        'id' => $messageId,
                        'type' => 'message',
                        'role' => 'assistant',
                        'content' => [],
                        'model' => $model,
                        'stop_reason' => null,
                        'stop_sequence' => null,
                        'usage' => [
                            'input_tokens' => 0,
                            'output_tokens' => 0
                        ]
                    ]
                ]) . "\n\n";

                // 发送 content_block_start 事件
                yield 'data: ' . json_encode([
                    'type' => 'content_block_start',
                    'index' => 0,
                    'content_block' => [
                        'type' => 'text',
                        'text' => ''
                    ]
                ]) . "\n\n";

                foreach ($result as $event) {
                    $anthropicEvents = $this->convertEventToAnthropic($event);
                    if (is_array($anthropicEvents)) {
                        foreach ($anthropicEvents as $anthropicEvent) {
                            if ($anthropicEvent) {
                                yield 'data: ' . json_encode($anthropicEvent) . "\n\n";
                            }
                        }
                    } elseif ($anthropicEvents) {
                        yield 'data: ' . json_encode($anthropicEvents) . "\n\n";
                    }
                }
            };

            $response = iterator($generator());

            return $response->header([
                'Content-Type'      => 'text/event-stream',
                'Cache-Control'     => 'no-cache, must-revalidate',
                'X-Accel-Buffering' => 'no',
            ]);
        } else {
            // 非流式响应，收集所有事件并转换为 Anthropic 格式
            $events = [];
            foreach ($result as $event) {
                $events[] = $event;
            }

            return json($this->convertResponseToAnthropic($events));
        }
    }

    /**
     * 将 Anthropic 格式转换为 OpenAI 格式
     */
    private function convertAnthropicToOpenAI($params)
    {
        $openaiParams = $params;

        // 处理 system 消息
        if (!empty($params['system'])) {
            // 将 system 参数转换为 messages 中的 system 消息
            $systemMessage = [
                'role'    => 'system',
                'content' => is_array($params['system']) ? $this->convertSystemContent($params['system']) : $params['system']
            ];

            // 将 system 消息插入到 messages 开头
            array_unshift($openaiParams['messages'], $systemMessage);

            // 移除单独的 system 参数
            unset($openaiParams['system']);
        }

        // 处理 messages 格式转换
        if (!empty($params['messages'])) {
            $openaiParams['messages'] = $this->convertMessages($params['messages']);
        }

        // 处理 tools 格式转换
        if (!empty($params['tools'])) {
            $openaiParams['tools'] = $this->convertTools($params['tools']);
        }

        // 处理 tool_choice 转换
        if (!empty($params['tool_choice'])) {
            $openaiParams['tool_choice'] = $this->convertToolChoice($params['tool_choice']);
        }

        // 处理 stop_sequences 转换为 stop
        if (!empty($params['stop_sequences'])) {
            $openaiParams['stop'] = $params['stop_sequences'];
            unset($openaiParams['stop_sequences']);
        }

        // 处理 thinking 参数
        if (!empty($params['thinking'])) {
            $openaiParams['thinking'] = $this->convertThinking($params['thinking']);
        }

        // 处理 metadata 参数
        if (!empty($params['metadata'])) {
            // OpenAI 格式中没有直接对应的 metadata，可以保留或忽略
            // 这里选择保留，让底层处理
        }

        return $openaiParams;
    }

    /**
     * 转换 system 内容格式
     */
    private function convertSystemContent($systemContent)
    {
        if (is_array($systemContent)) {
            $text = '';
            foreach ($systemContent as $item) {
                if (isset($item['type']) && $item['type'] === 'text') {
                    $text .= $item['text'] ?? '';
                }
            }
            return $text;
        }
        return $systemContent;
    }

    /**
     * 转换消息格式
     */
    private function convertMessages($messages)
    {
        return array_map(function ($message) {
            $role = $message['role'] ?? '';
            $content = $message['content'] ?? '';

            switch ($role) {
                case 'user':
                    // Anthropic 用户消息可能包含复杂内容结构
                    if (is_array($content)) {
                        $content = array_map(function ($item) {
                            // 转换图片格式
                            if (isset($item['type']) && $item['type'] === 'image') {
                                $imageUrl = '';
                                if (isset($item['source'])) {
                                    $source = $item['source'];
                                    if ($source['type'] === 'base64') {
                                        $imageUrl = 'data:' . $source['media_type'] . ';base64,' . $source['data'];
                                    } elseif (isset($source['url'])) {
                                        $imageUrl = $source['url'];
                                    }
                                }
                                return [
                                    'type' => 'image_url',
                                    'image_url' => [
                                        'url' => $imageUrl
                                    ]
                                ];
                            }
                            return $item;
                        }, $content);
                    }
                    break;

                case 'assistant':
                    // 处理 assistant 消息中的 tool_use
                    if (is_array($content)) {
                        $textContent = '';
                        $toolCalls = [];

                        foreach ($content as $item) {
                            if (isset($item['type'])) {
                                switch ($item['type']) {
                                    case 'text':
                                        $textContent .= $item['text'] ?? '';
                                        break;
                                    case 'tool_use':
                                        $toolCalls[] = [
                                            'id' => $item['id'],
                                            'type' => 'function',
                                            'function' => [
                                                'name' => $item['name'],
                                                'arguments' => json_encode($item['input'] ?? [])
                                            ]
                                        ];
                                        break;
                                }
                            }
                        }

                        $message['content'] = $textContent;
                        if (!empty($toolCalls)) {
                            $message['tool_calls'] = $toolCalls;
                        }
                    }
                    break;

                case 'tool':
                    // Anthropic 的 tool 结果格式转换
                    // Anthropic 使用 tool_result 类型，需要转换为 OpenAI 的 tool 角色
                    if (is_array($content)) {
                        foreach ($content as $item) {
                            if (isset($item['type']) && $item['type'] === 'tool_result') {
                                $message['tool_call_id'] = $item['tool_use_id'];
                                $message['content'] = $item['content'] ?? '';
                                break;
                            }
                        }
                    }
                    break;
            }

            return $message;
        }, $messages);
    }

    /**
     * 转换工具格式
     */
    private function convertTools($tools)
    {
        return array_map(function ($tool) {
            // Anthropic 工具格式转换为 OpenAI 格式
            if (isset($tool['name']) && isset($tool['input_schema'])) {
                return [
                    'type' => 'function',
                    'function' => [
                        'name' => $tool['name'],
                        'description' => $tool['description'] ?? '',
                        'parameters' => $tool['input_schema']
                    ]
                ];
            }

            // 如果已经是 OpenAI 格式，直接返回
            return $tool;
        }, $tools);
    }

    /**
     * 转换 tool_choice 格式
     */
    private function convertToolChoice($toolChoice)
    {
        // Anthropic 的 tool_choice 格式
        if (isset($toolChoice['type'])) {
            switch ($toolChoice['type']) {
                case 'auto':
                    return 'auto';
                case 'any':
                    return 'required';
                case 'tool':
                    if (isset($toolChoice['name'])) {
                        return [
                            'type' => 'function',
                            'function' => [
                                'name' => $toolChoice['name']
                            ]
                        ];
                    }
                    break;
                case 'none':
                    return 'none';
            }
        }

        return $toolChoice;
    }

    /**
     * 转换 thinking 参数
     */
    private function convertThinking($thinking)
    {
        // Anthropic 的 thinking 格式转换
        if (isset($thinking['type'])) {
            switch ($thinking['type']) {
                case 'enabled':
                    return 'enabled';
                case 'disabled':
                    return 'disabled';
            }
        }

        return $thinking;
    }

    /**
     * 转换错误响应为 Anthropic 格式
     */
    private function convertErrorToAnthropic($error)
    {
        return [
            'type' => 'error',
            'error' => [
                'type' => 'invalid_request_error',
                'message' => $error['error']['message'] ?? $error['message'] ?? 'Unknown error'
            ]
        ];
    }

    /**
     * 转换流式事件为 Anthropic 格式
     */
    private function convertEventToAnthropic($event)
    {
        $events = [];

        // 处理 usage 事件
        if (isset($event['usage'])) {
            $events[] = [
                'type' => 'content_block_stop',
                'index' => 0
            ];

            $events[] = [
                'type' => 'message_delta',
                'delta' => [
                    'stop_reason' => $this->convertStopReason($event['finish_reason'] ?? 'stop'),
                    'stop_sequence' => null
                ],
                'usage' => [
                    'output_tokens' => $event['usage']['completion_tokens'] ?? 0
                ]
            ];

            $events[] = [
                'type' => 'message_stop'
            ];

            return $events;
        }

        // 处理 delta 事件
        if (isset($event['delta'])) {
            $delta = $event['delta'];

            // 处理文本内容
            if (isset($delta['content']) && $delta['content'] !== '') {
                return [
                    'type' => 'content_block_delta',
                    'index' => 0,
                    'delta' => [
                        'type' => 'text_delta',
                        'text' => $delta['content']
                    ]
                ];
            }

            // 处理工具调用开始
            if (isset($delta['tool_calls'])) {
                $toolCall = $delta['tool_calls'][0];

                // 如果是新的工具调用，先发送 content_block_stop 和 content_block_start
                if (isset($toolCall['id'])) {
                    $events[] = [
                        'type' => 'content_block_stop',
                        'index' => 0
                    ];

                    $events[] = [
                        'type' => 'content_block_start',
                        'index' => 1,
                        'content_block' => [
                            'type' => 'tool_use',
                            'id' => $toolCall['id'],
                            'name' => $toolCall['function']['name'] ?? '',
                            'input' => []
                        ]
                    ];
                }

                // 发送工具调用的参数增量
                if (isset($toolCall['function']['arguments'])) {
                    $events[] = [
                        'type' => 'content_block_delta',
                        'index' => 1,
                        'delta' => [
                            'type' => 'input_json_delta',
                            'partial_json' => $toolCall['function']['arguments']
                        ]
                    ];
                }

                return $events;
            }
        }

        // 处理完成事件
        if (isset($event['finish_reason'])) {
            return [
                'type' => 'content_block_stop',
                'index' => 0
            ];
        }

        return null;
    }

    /**
     * 转换完整响应为 Anthropic 格式
     */
    private function convertResponseToAnthropic($events)
    {
        $content = [];
        $usage = null;
        $stopReason = 'end_turn';
        $messageId = 'msg_' . uniqid();

        foreach ($events as $event) {
            if (isset($event['message'])) {
                $message = $event['message'];

                // 处理文本内容
                if (!empty($message['content'])) {
                    $content[] = [
                        'type' => 'text',
                        'text' => $message['content']
                    ];
                }

                // 处理工具调用
                if (!empty($message['tool_calls'])) {
                    foreach ($message['tool_calls'] as $toolCall) {
                        $content[] = [
                            'type' => 'tool_use',
                            'id' => $toolCall['id'],
                            'name' => $toolCall['function']['name'],
                            'input' => json_decode($toolCall['function']['arguments'] ?? '{}', true)
                        ];
                    }
                }
            }

            if (isset($event['usage'])) {
                $usage = [
                    'input_tokens' => $event['usage']['prompt_tokens'] ?? 0,
                    'output_tokens' => $event['usage']['completion_tokens'] ?? 0
                ];
            }

            if (isset($event['finish_reason'])) {
                $stopReason = $this->convertStopReason($event['finish_reason']);
            }
        }

        return [
            'id' => $messageId,
            'type' => 'message',
            'role' => 'assistant',
            'content' => $content,
            'model' => $this->request->param('model', 'claude-3-sonnet'),
            'stop_reason' => $stopReason,
            'stop_sequence' => null,
            'usage' => $usage
        ];
    }

    /**
     * 转换停止原因
     */
    private function convertStopReason($openaiStopReason)
    {
        return match ($openaiStopReason) {
            'stop' => 'end_turn',
            'length' => 'max_tokens',
            'tool_calls' => 'tool_use',
            'content_filter' => 'refusal',
            default => 'end_turn'
        };
    }
}
