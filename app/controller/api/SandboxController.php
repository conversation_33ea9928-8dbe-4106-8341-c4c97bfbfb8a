<?php

namespace app\controller\api;

use app\lib\K8s;
use app\lib\llm\Exception;
use app\lib\llm\tool\result\Error;
use app\lib\llm\tool\result\Image;
use app\lib\llm\tool\result\Raw;
use GuzzleHttp\Client;
use K8s\Api\Model\Api\Batch\v1\Job;
use K8s\Api\Model\Api\Core\v1\Container;
use K8s\Api\Model\Api\Core\v1\HTTPGetAction;
use K8s\Api\Model\Api\Core\v1\Pod;
use K8s\Api\Model\Api\Core\v1\PodTemplateSpec;
use K8s\Api\Model\Api\Core\v1\Probe;
use K8s\Api\Model\Api\Core\v1\ResourceRequirements;
use think\exception\ValidateException;
use think\facade\Log;
use think\annotation\route\Post;
use think\annotation\route\Resource;
use think\helper\Arr;

#[Resource('sandbox')]
class SandboxController extends Controller
{
    public function save()
    {
        //创建虚拟机
        $k8s = K8s::inCluster();

        $image     = env('CODE_INTERPRETER_IMAGE', 'registry-vpc.cn-shanghai.aliyuncs.com/topthink/code-interpreter:latest');
        $container = new Container('code-interpreter', $image);
        $container->setImagePullPolicy('Always');

        $startupProbe = new Probe();
        $startupProbe->setFailureThreshold(5);
        $startupProbe->setTimeoutSeconds(5);
        $startupProbe->setPeriodSeconds(1);

        $httpGet = new HTTPGetAction(49999);
        $httpGet->setPath('/health');

        $startupProbe->setHttpGet($httpGet);

        $container->setStartupProbe($startupProbe);

        if (!env('APP_DEBUG', false)) {
            $resources = new ResourceRequirements();
            $resources->setRequests([
                'cpu'    => 0.5,
                'memory' => '1Gi',
            ]);
            $container->setResources($resources);
        }

        $template = new PodTemplateSpec(null, [$container]);
        $template->setRestartPolicy('Never');
        $template->setActiveDeadlineSeconds(300);

        $job = new Job(null, $template);

        $job->setTtlSecondsAfterFinished(0);
        $id   = uniqid();
        $name = "sandbox-{$id}";
        $job->setName($name);
        $job->setBackoffLimit(0);

        $k8s->create($job);

        //等待虚拟机启动
        $time = 0;
        while (true) {
            /** @var Job $status */
            $status = $k8s->readStatus($name, Job::class);
            if ($status->getStatus()->getReady()) {
                break;
            }
            //最多等待10s
            if ($time > 20) {
                throw new Exception('虚拟机启动超时');
            }
            $time++;
            //等待100ms
            usleep(500 * 1000);
        }

        return json(['id' => $id]);
    }

    #[Post('sandbox/:id/execute')]
    public function execute($id)
    {
        $data = $this->validate([
            'code'  => 'require',
            'files' => 'array',
        ]);

        $k8s = K8s::inCluster();
        try {
            $job = $k8s->read("sandbox-{$id}", Job::class);
        } catch (\Exception) {
            abort(404, '虚拟机不存在');
        }

        $pods = $k8s->listNamespaced(Pod::class, ['labelSelector' => "job-name={$job->getName()}"]);
        $pod  = Arr::first($pods);
        $ip   = $pod->getStatus()->getPodIP();

        $client = new Client([
            'base_uri' => "http://{$ip}:49999",
        ]);

        //上传文件
        if (!empty($data['files'])) {
            foreach ($data['files'] as $file) {
                if (empty($file['url']) || empty($file['name'])) {
                    throw new ValidateException('missing required properties in files');
                }
                $client->post("/upload", [
                    'multipart' => [
                        [
                            'name'     => 'file',
                            'contents' => fopen($file['url'], 'r'),
                            'filename' => $file['name'],
                        ],
                    ],
                ]);
            }
        }

        //执行代码
        $res = $client->post("/execute", [
            'json' => [
                'code' => $data['code'],
            ],
        ]);

        $response = $res->getBody()->getContents();

        //使用回车分割
        $lines = explode("\n", trim($response));

        foreach ($lines as $line) {
            $data = json_decode($line, true);
            if (isset($data['type'])) {
                switch ($data['type']) {
                    case 'error':
                        $result = new Error(new \Exception($data['value']));
                        break;
                    case 'stdout':
                    case 'result':
                        if (isset($data['png'])) {
                            $result = new Image("data:image/png;base64,{$data['png']}");
                        } else {
                            $result = new Raw($data['text']);
                        }
                        break;
                    case 'end_of_execution':
                    case 'number_of_executions':
                        break;
                    default:
                        Log::info($line);
                        break;
                }
            }
        }

        if (empty($result)) {
            $result = new Raw('no response');
        }

        return json($result);
    }

    public function delete($id)
    {
        $k8s = K8s::inCluster();
        try {
            $job = $k8s->read("sandbox-{$id}", Job::class);
            $k8s->delete($job, [
                'propagationPolicy' => 'Background',
            ]);
        } catch (\Exception) {

        }
    }
}
